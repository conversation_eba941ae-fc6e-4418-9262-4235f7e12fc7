using Microsoft.EntityFrameworkCore;
using Multiempresa.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Zendar.Data.Contexts;
using Zendar.Data.Helpers;
using Zendar.Data.Models.Aplicacao;
using Zendar.Data.Models.CampanhaPromocional;
using Zendar.Data.Models.PesquisaDeProduto;
using Zendar.Data.Models.PesquisaDeProduto.ProdutoFiltros;
using Zendar.Data.ViewModels;
using Zendar.Data.ViewModels.PesquisaProduto;
using DynamicLinq = System.Linq.Dynamic.Core;

namespace Zendar.Data.Repository.Aplicacao.PesquisaProdutoRepository
{
    public class PesquisaProdutoRepository : RepositoryAplicacao<PesquisaProduto>, IPesquisaProdutoRepository
    {
        private readonly IProdutoFiltro _produtoFiltro;

        public PesquisaProdutoRepository(
            AplicacaoContexto db) : base(db)
        {
            //_produtoFiltro = new ProdutoFiltroFullText();
            _produtoFiltro = new ProdutoFiltroTextoSimples();
        }

        #region [CRUD]
        public async Task AlterarDescricaoCor(
            Guid id,
            string descricao)
        {
            await Db.Database.ExecuteSqlRawAsync(@$"
                UPDATE
                    [PesquisaProduto]
                SET
                    [Cor] = '{descricao}',
                    [DescricaoCompleta] = [dbo].[REMOVER_APOSTROFE](
						CONCAT(
							[SKUIdentificador],
							(' ' + [Nome]),
							' {descricao}',
							(' ' + [Tamanho]),
							(' ' + [Marca]),
							(' ' + CAST([SequenciaCodigoBarras] AS VARCHAR(20)))
						)
					)
                WHERE
                    [CorId] = '{id}' AND
                    [Cor] <> '{descricao}';
            ");
        }

        public async Task AlterarDescricaoTamanho
            (Guid id,
            string descricao)
        {
            await Db.Database.ExecuteSqlRawAsync(@$"
                UPDATE
                    [PesquisaProduto]
                SET
                    [Tamanho] = '{descricao}',
                    [DescricaoCompleta] = [dbo].[REMOVER_APOSTROFE](
						CONCAT(
							[SKUIdentificador],
							(' ' + [Nome]),
							(' ' + [Cor]),
							' {descricao}',
							(' ' + [Marca]),
							(' ' + CAST([SequenciaCodigoBarras] AS VARCHAR(20)))
						)
					)
                WHERE
                    [TamanhoId] = '{id}' AND
                    [Tamanho] <> '{descricao}';
            ");
        }

        public async Task AlterarNomeMarca(
            Guid id,
            string nome)
        {
            await Db.Database.ExecuteSqlRawAsync(@$"
                UPDATE
                    [PesquisaProduto]
                SET
                    [Marca] = '{nome}',
                    [DescricaoCompleta] = [dbo].[REMOVER_APOSTROFE](
						CONCAT(
							[SKUIdentificador],
							(' ' + [Nome]),
							(' ' + [Cor]),
							(' ' + [Tamanho]),
							' {nome}',
							(' ' + CAST([SequenciaCodigoBarras] AS VARCHAR(20)))
						)
					)
                WHERE
                    [MarcaId] = '{id}' AND
                    [Marca] <> '{nome}';
            ");
        }

        public async Task DeleteAll()
        {
            await Db.Database.ExecuteSqlRawAsync("DELETE FROM [PesquisaProduto]");
        }
        #endregion

        #region [Pesquisa por id]
        public async Task<List<PesquisaProduto>> ObterPorProdutoId(Guid produtoId)
        {
            return await DbSet.Where(x => x.ProdutoId == produtoId).ToListAsync();
        }
        #endregion

        #region [Listar Paginado]
        public GridPaginadaRetorno<IdNomeViewModel> ListarSelectProdutoCorTamanhoPaginado(
            GridPaginadaConsulta gridConsulta,
            PesquisaProdutoFiltro filtroProduto)
        {
            GridPaginadaRetorno<PesquisaProduto> gridAuxiliar = new();

            var query = DbSet.AsQueryable();

            var produtos = _produtoFiltro.FiltrarProdutoCorTamanhos(query, filtroProduto)
                                        .Where(p => p.Ativo)
                                        .Select(p => new PesquisaProduto
                                        {
                                            Nome = p.Nome,
                                            ProdutoCorTamanhoId = p.ProdutoCorTamanhoId,
                                            Cor = p.Cor,
                                            Tamanho = p.Tamanho,
                                            ProdutoCorTamanho = new ProdutoCorTamanho
                                            {
                                                Tamanho = new Tamanho
                                                {
                                                    SequenciaOrdenacao = p.ProdutoCorTamanho.Tamanho.SequenciaOrdenacao
                                                },
                                            }
                                        })
                                        .OrderBy(x => x.Nome)
                                        .ThenBy(x => x.Cor)
                                        .ThenBy(x => x.ProdutoCorTamanho.Tamanho.SequenciaOrdenacao);

            gridAuxiliar.CarregarPaginacao(produtos, gridConsulta);

            return new GridPaginadaRetorno<IdNomeViewModel>()
            {
                Registros = gridAuxiliar.Registros.ConvertAll(
                    r => new IdNomeViewModel { Id = r.ProdutoCorTamanhoId, Nome = r.ObterNomeProdutoCorTamanho() }),
                Total = gridAuxiliar.Total
            };
        }
        #endregion

        #region [Listagem]
        public async Task<List<ProdutoSelectPdvViewModel>> ListarSelectPdv(PesquisaProdutoFiltro filtro)
        {
            var queryBase = DbSet
                    .Where(p => p.Ativo && p.ProdutoCorTamanho.Ativo);

            if (filtro.PesquisarPorCodigo)
            {
                var produto = await _produtoFiltro.FiltrarPorCodigo(queryBase, filtro.FiltroCodigo)
                    .Select(ProdutoSelectPdvViewModel.Selector)
                    .FirstOrDefaultAsync();

                if (produto is not null)
                {
                    return new()
                    {
                        produto.ToSelectPdvAdicionarAutomaticamenteViewModel(filtro.ToString())
                    };
                }
            }

            var produtosCores = await _produtoFiltro.FiltrarPorDescricao(queryBase, filtro.FiltroDescricao)
                        .OrderBy(x => x.Nome)
                        .Select(x => x.ProdutoCorId)
                        .Distinct()
                        .Take(20)
                        .ToListAsync();

            var produtos = await Db.ProdutoCor
                        .Where(x => produtosCores.Contains(x.Id))
                        .Select(x => new ProdutoCor
                        {
                            Id = x.Id,
                            Cor = new Cor
                            {
                                Descricao = x.Cor.Descricao,
                                PadraoSistema = x.Cor.PadraoSistema,
                            },
                            Produto = new Produto
                            {
                                Nome = x.Produto.Nome
                            }
                        })
                        .OrderBy(x => x.Produto.Nome)
                        .Take(20)
                        .ToListAsync();

            return produtos.ConvertAll(p => new ProdutoSelectPdvViewModel
            {
                ProdutoCorId = p.Id,
                Nome = p.FormatarDescricaoCompletaProduto(),
                AdicionarItemAutomaticamente = false,
            });
        }

        public async Task<List<ProdutoSelectPesquisaCodigoViewModel>> ListarSelectPesquisaCodigo(PesquisaProdutoFiltro filtro)
        {
            var queryBase = DbSet
                    .Where(p => p.Ativo && p.ProdutoCorTamanho.Ativo);

            if (filtro.PesquisarPorCodigo)
            {
                var produto = await _produtoFiltro.FiltrarPorCodigo(queryBase, filtro.FiltroCodigo)
                                              .Select(ProdutoSelectPesquisaCodigoViewModel.Selector)
                                              .FirstOrDefaultAsync();

                if (produto is not null)
                {
                    return new()
                    {
                        produto.AdicionarAutomaticamentePorCodigoViewModel()
                    };
                }
            }

            return new();
        }

        public List<IGrouping<Guid, PesquisaProduto>> ListarSelectProduto(PesquisaProdutoFiltro filtroProduto)
        {
            var query = DbSet.AsQueryable();

            var produtos = _produtoFiltro.FiltrarProdutos(query, filtroProduto);

            return produtos
                        .Where(p => p.Ativo && p.ProdutoCorTamanho.Ativo)
                        .Select(p => new PesquisaProduto
                        {
                            ProdutoId = p.ProdutoId,
                            Nome = p.Nome
                        })
                        .AsEnumerable()
                        .GroupBy(x => x.ProdutoId)
                        .Take(20)
                        .ToList();
        }

        public async Task<List<PesquisaProduto>> ListarSelectProdutoCorTamanho(PesquisaProdutoFiltro filtroProduto)
        {
            var query = DbSet.AsQueryable();

            var produtos = _produtoFiltro.FiltrarProdutoCorTamanhos(query, filtroProduto);

            return await produtos
                        .Where(p => p.Ativo)
                        .Select(p => new PesquisaProduto
                        {
                            Nome = p.Nome,
                            ProdutoCorTamanhoId = p.ProdutoCorTamanhoId,

                            ProdutoCor = new ProdutoCor
                            {
                                Cor = new Cor
                                {
                                    Descricao = p.ProdutoCor.Cor.Descricao,
                                    PadraoSistema = p.ProdutoCor.Cor.PadraoSistema
                                },
                            },
                            ProdutoCorTamanho = new ProdutoCorTamanho
                            {
                                Tamanho = new Tamanho
                                {
                                    Descricao = p.ProdutoCorTamanho.Tamanho.Descricao,
                                    PadraoSistema = p.ProdutoCorTamanho.Tamanho.PadraoSistema
                                },
                            }
                        })
                        .OrderBy(x => x.Nome)
                        .Take(20)
                        .ToListAsync();
        }

        public IEnumerable<IGrouping<Guid, PesquisaProduto>> ListarSelectProdutoCorAgrupado(PesquisaProdutoFiltro filtroProduto)
        {
            var query = DbSet.AsQueryable();

            var produtos = _produtoFiltro.FiltrarProdutoCores(query, filtroProduto);

            return produtos
                        .Where(p => p.Ativo)
                        .Select(p => new PesquisaProduto
                        {
                            Nome = p.Nome,
                            ProdutoCorTamanhoId = p.ProdutoCorTamanhoId,
                            ProdutoCorId = p.ProdutoCorId,

                            ProdutoCor = new ProdutoCor
                            {
                                Cor = new Cor
                                {
                                    Descricao = p.ProdutoCor.Cor.Descricao,
                                    PadraoSistema = p.ProdutoCor.Cor.PadraoSistema
                                },
                            },
                            ProdutoCorTamanho = new ProdutoCorTamanho
                            {
                                Tamanho = new Tamanho
                                {
                                    Descricao = p.ProdutoCorTamanho.Tamanho.Descricao,
                                    PadraoSistema = p.ProdutoCorTamanho.Tamanho.PadraoSistema
                                },
                            }
                        })
                        .OrderBy(x => x.Nome)
                        .Take(20)
                        .AsEnumerable()
                        .GroupBy(p => p.ProdutoCorId);
        }

        public async Task<(List<Guid>, int)> ListarIdProduto(PesquisaProdutoFiltro filtroProduto, GridPaginadaConsulta gridPaginada = null)
        {
            var query = DbSet.AsQueryable();

            var produtos = _produtoFiltro.FiltrarProdutos(query, filtroProduto)
                .Where(p => p.Ativo && p.ProdutoCorTamanho.Ativo);

            // Aplicar ordenação se gridPaginada não for nula e tiver informações de ordenação
            if (gridPaginada != null && !string.IsNullOrEmpty(gridPaginada.CampoOrdenacao))
            {
                produtos = DynamicLinq.DynamicQueryableExtensions.OrderBy(produtos, $"{gridPaginada.CampoOrdenacao} {gridPaginada.DirecaoOrdenacao}");
            }

            // Para manter a ordenação, precisamos agrupar por ProdutoId e pegar o primeiro de cada grupo
            var queryDistinctIds = produtos
                .GroupBy(p => p.ProdutoId)
                .Select(g => g.Key);

            if (gridPaginada is null)
            {
                return (await queryDistinctIds
                            .Take(20)
                            .ToListAsync(), 20);
            }

            var totalCount = await queryDistinctIds.CountAsync();

            return (await queryDistinctIds
                            .Skip(gridPaginada.Skip)
                            .Take(gridPaginada.TamanhoPagina)
                            .ToListAsync(), totalCount);
        }

        public async Task<List<Guid>> ListarTodosProdutoId(PesquisaProdutoFiltro filtroProduto)
        {
            var query = DbSet.AsQueryable();

            var produtos = _produtoFiltro.FiltrarProdutos(query, filtroProduto);

            return await produtos
                            //.Where(p => p.ProdutoCorTamanho.Ativo)
                            .Select(p => p.ProdutoId)
                            .Distinct()
                            .ToListAsync();
        }

        public IEnumerable<IdNomeViewModel> ListarSelectProdutoSimples(PesquisaProdutoFiltro filtroProduto)
        {
            var query = DbSet.AsQueryable();

            var produtos = _produtoFiltro.FiltrarProdutos(query, filtroProduto);

            return produtos
                        .Where(p => p.Ativo && p.Produto.TipoProduto == TipoProduto.PRODUTO_SIMPLES)
                        .Select(p => new IdNomeViewModel(p.ProdutoId, p.Nome))
                        .AsEnumerable()
                        .GroupBy(x => x.Id)
                        .Take(20)
                        .SelectMany(x => x)
                        .ToList();
        }

        public IQueryable<PromocaoItem> QueryableItensPromocao(Guid promocaoId, PesquisaProdutoFiltro filtroProduto)
        {
            var query = DbSet.Where(p => p.ProdutoCorTamanho.PromocaoItens.Any(pi => pi.PromocaoId == promocaoId));

            if (filtroProduto is not null)
            {
                query = _produtoFiltro.FiltrarProdutos(query, filtroProduto);
            }

            return query.SelectMany(p => p.ProdutoCorTamanho.PromocaoItens)
                        .Include(pi => pi.ProdutoCorTamanho)
                        .ThenInclude(pct => pct.ProdutoCor)
                        .ThenInclude(pc => pc.Produto)
                        .ThenInclude(p => p.ProdutoPrecoLojas);
        }
        #endregion
    }
}
